from apify_client import ApifyClient
import json

# Initialize the ApifyClient with your API token
client = ApifyClient("**********************************************")

# Prepare the Actor input
run_input = { "profileUrls": [
        "https://www.linkedin.com/in/abdullah-2k3/",
        "https://www.linkedin.com/in/shahzadwaris92/",
    ] }

# Run the Actor and wait for it to finish
run = client.actor("2SyF0bVxmgGr8IVCZ").call(run_input=run_input)

# Fetch and print Actor results from the run's dataset (if there are any)
for item in client.dataset(run["defaultDatasetId"]).iterate_items():
    print(item)

# Fetch results
results = []
for item in client.dataset(run["defaultDatasetId"]).iterate_items():
    results.append(item)

# Save to JSON file
with open("apify_results.json", "w") as f:
    json.dump(results, f, indent=4)

print("Data saved to apify_results.json")    